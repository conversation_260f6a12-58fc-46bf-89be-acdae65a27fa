package service

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/util"
	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/process"
	"k8s.io/apimachinery/pkg/util/wait"
)

// DockerAppManager Docker应用管理器
type DockerAppManager struct {
	PackageInfoProvider
}

// NewDockerAppManager 创建Docker应用管理器
func NewDockerAppManager(app string, baseDir string) (AppManagerInterface, error) {
	pkg, err := NewAppPackageInfo(app, constants.DeployModeSingle, baseDir)
	if err != nil {
		return nil, err
	}
	return &DockerAppManager{
		PackageInfoProvider: pkg,
	}, nil
}

// Install 安装应用
func (m *DockerAppManager) Install(ctx context.Context, config *AppConfig) error {
	logger.Infof(ctx, "Starting installation of application: %s", config.Name)

	envMap, err := util.ConvertYamlToEnvMap(config.EnvFilePath)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert environment variables: %v", err)
		return err
	}

	// 执行节点初始化脚本
	logger.Infof(ctx, "Executing node initialization script")
	nodeInitScriptPath := m.GetNodeInitScriptPath()
	opt := &util.ExecOption{
		FilePath: nodeInitScriptPath,
		CmdName:  constants.ShellCmd,
		Args:     []string{nodeInitScriptPath},
		Envs:     envMap,
		WorkDir:  m.GetComponentPath(),
		SetHome:  true,
	}
	if err := util.ExecFileIfExists(ctx, opt); err != nil {
		logger.Errorf(ctx, "Installation failed at node initialization script stage")
		return err
	}

	// 执行安装前脚本
	logger.Infof(ctx, "Executing pre-installation script")
	preInstallScriptPath := m.GetPreInstallScriptPath()
	opt = &util.ExecOption{
		FilePath: preInstallScriptPath,
		CmdName:  constants.ShellCmd,
		Args:     []string{preInstallScriptPath},
		Envs:     envMap,
		WorkDir:  m.GetComponentPath(),
		SetHome:  true,
	}
	if err := util.ExecFileIfExists(ctx, opt); err != nil {
		logger.Errorf(ctx, "Installation failed at pre-installation script stage")
		return err
	}

	// 执行docker-compose
	logger.Infof(ctx, "Starting docker-compose")
	dockerComposePath := m.GetDockerComposePath()

	// 准备docker-compose命令参数
	dockerComposeArgs := []string{constants.DockerComposeFileFlag, dockerComposePath}

	// 如果EnvFilePath不为空，转换环境变量格式并创建临时文件
	var tmpEnvFile string
	if config.EnvFilePath != "" {
		var err error
		tmpEnvFile, err = util.ConvertYamlToEnvFile(config.EnvFilePath)
		if err != nil {
			logger.Errorf(ctx, "Failed to convert environment variables: %v", err)
			return err
		}
		// 添加环境变量文件参数
		dockerComposeArgs = append(dockerComposeArgs, constants.DockerComposeEnvFlag, tmpEnvFile)
		// 延迟删除临时文件
		defer os.Remove(tmpEnvFile)
	}

	// 添加up和detach参数
	dockerComposeArgs = append(dockerComposeArgs, constants.DockerComposeUpCmd, constants.DockerComposeDetachFlag)

	opt = &util.ExecOption{
		FilePath: dockerComposePath,
		CmdName:  constants.DockerComposeCmd,
		Args:     dockerComposeArgs,
		Envs:     envMap,
		WorkDir:  m.GetComponentPath(),
		SetHome:  true,
	}
	if err := util.ExecFileIfExists(ctx, opt); err != nil {
		logger.Errorf(ctx, "Installation failed at docker-compose stage")
		return err
	}

	// 安装完后，对应用进行健康检查
	logger.Infof(ctx, "Starting health check for application: %s", config.Name)

	err = m.waitForAppHealthy(ctx, config.Name)
	if err != nil {
		logger.Errorf(ctx, "Health check failed for application %s after timeout: %v", config.Name, err)
		return fmt.Errorf("installation failed at health check stage: %w", err)
	}

	logger.Infof(ctx, "Application %s installation completed successfully", config.Name)
	return nil
}

// Uninstall 卸载应用
func (m *DockerAppManager) Uninstall(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Starting uninstallation of application: %s", appName)

	switch m.HasUninstallScript() {
	case true:
		// 执行卸载脚本
		logger.Infof(ctx, "Executing uninstallation script if exists")
		uninstallScriptPath := m.GetUninstallScriptPath()
		opt := &util.ExecOption{
			FilePath: uninstallScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{uninstallScriptPath},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Uninstallation failed at uninstallation script stage: %v", err)
			return err
		}
	case false:
		// 执行docker-compose down
		dockerComposePath := m.GetDockerComposePath()
		opt := &util.ExecOption{
			FilePath: dockerComposePath,
			CmdName:  constants.DockerComposeCmd,
			Args:     []string{constants.DockerComposeFileFlag, dockerComposePath, constants.DockerComposeDownCmd},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Uninstallation failed at docker-compose down stage: %v", err)
			return err
		}
	}

	logger.Infof(ctx, "Application %s uninstallation completed successfully", appName)
	return nil
}

// Start 启动应用
func (m *DockerAppManager) Start(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Starting application: %s", appName)

	switch m.HasStartScript() {
	case true:
		// 执行启动脚本
		logger.Infof(ctx, "Executing start script if exists")
		startScriptPath := m.GetStartScriptPath()
		opt := &util.ExecOption{
			FilePath: startScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{startScriptPath},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Failed to start application at start script stage: %v", err)
			return err
		}
	case false:
		// 执行docker-compose up
		logger.Infof(ctx, "Executing docker-compose up if exists")
		dockerComposePath := m.GetDockerComposePath()
		opt := &util.ExecOption{
			FilePath: dockerComposePath,
			CmdName:  constants.DockerComposeCmd,
			Args:     []string{constants.DockerComposeFileFlag, dockerComposePath, constants.DockerComposeStartCmd},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Failed to start application at docker-compose stage: %v", err)
			return err
		}
	}

	// 启动后进行健康检查
	logger.Infof(ctx, "Starting health check for application: %s", appName)
	if err := m.waitForAppHealthy(ctx, appName); err != nil {
		logger.Errorf(ctx, "Health check failed for application %s after timeout: %v", appName, err)
		return fmt.Errorf("start failed at health check stage: %w", err)
	}

	logger.Infof(ctx, "Application %s start and health check completed successfully", appName)
	return nil
}

// Stop 停止应用
func (m *DockerAppManager) Stop(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Stopping application: %s", appName)

	switch m.HasStopScript() {
	case true:
		// 执行停止脚本
		stopScriptPath := m.GetStopScriptPath()
		opt := &util.ExecOption{
			FilePath: stopScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{stopScriptPath},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Failed to stop application at stop script stage: %v", err)
		}
	case false:
		// 执行docker-compose stop
		dockerComposePath := m.GetDockerComposePath()
		opt := &util.ExecOption{
			FilePath: dockerComposePath,
			CmdName:  constants.DockerComposeCmd,
			Args:     []string{constants.DockerComposeFileFlag, dockerComposePath, constants.DockerComposeStopCmd},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Failed to stop application: %v", err)
			return err
		}
	}
	logger.Infof(ctx, "Application %s stopped successfully", appName)
	return nil
}

// Status 检查应用状态
func (m *DockerAppManager) singleHealthCheck(ctx context.Context, appName string) (bool, error) {
	logger.Infof(ctx, "Checking application status: %s", appName)

	switch m.HasHealthCheckScript() {
	case true:
		// 执行健康检查脚本
		healthCheckScriptPath := m.GetHealthCheckScriptPath()
		opt := &util.ExecOption{
			FilePath: healthCheckScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{healthCheckScriptPath},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Application %s health check failed: %v", appName, err)
			return false, err
		}

		logger.Infof(ctx, "Application %s is healthy", appName)
		return true, nil
	case false:
		// 执行 docker-compose ps 命令检查所有容器状态
		dockerComposePath := m.GetDockerComposePath()
		executor := process.NewExecutor(constants.DockerComposeCmd).
			WithArgs(constants.DockerComposeFileFlag, dockerComposePath, constants.DockerComposePsCmd, constants.DockerComposeAllFlag)
		output, err := executor.ExecRetOut(ctx)
		if err != nil {
			logger.Errorf(ctx, "Failed to execute docker-compose ps command: %v", err)
			return false, err
		}

		// 检查输出中是否包含非运行状态的容器
		if !strings.Contains(output, " Up ") || strings.Contains(output, "Exit") {
			logger.Errorf(ctx, "Found containers not running: %s", output)
			return false, fmt.Errorf("containers not running")
		}
	}
	return true, nil

}

// Status 检查应用状态
func (m *DockerAppManager) Status(ctx context.Context, appName string) (AppRunStatus, error) {
	ctx, cancel := context.WithTimeout(ctx, constants.DefaultSingleHealthCheckTimeout*time.Second)
	defer cancel()
	return m.healthCheckWithTimeout(ctx, appName)
}

func (m *DockerAppManager) Init(ctx context.Context, config *AppConfig) error {
	envMap, err := util.ConvertYamlToEnvMap(config.EnvFilePath)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert environment variables: %v", err)
		return err
	}

	// 执行节点初始化脚本
	logger.Infof(ctx, "Executing node initialization script")
	nodeInitScriptPath := m.GetNodeInitScriptPath()
	opt := &util.ExecOption{
		FilePath: nodeInitScriptPath,
		CmdName:  constants.ShellCmd,
		Args:     []string{nodeInitScriptPath},
		Envs:     envMap,
		WorkDir:  m.GetComponentPath(),
		SetHome:  true,
	}
	if err := util.ExecFileIfExists(ctx, opt); err != nil {
		logger.Errorf(ctx, "Installation failed at node initialization script stage")
		return err
	}

	return nil
}

func (m *DockerAppManager) healthCheckWithTimeout(ctx context.Context, appName string) (AppRunStatus, error) {
	var (
		status          bool
		lastKnownStatus AppRunStatus
		err             error
	)

	status, err = m.singleHealthCheck(ctx, appName)
	if err != nil {
		logger.Warnf(ctx, "Health check failed for application %s: %v", appName, err)
	}

	switch status {
	case true:
		lastKnownStatus = AppRunStatus_APP_RUN_NORMAL
	case false:
		lastKnownStatus = AppRunStatus_APP_RUN_ABNORMAL
	}

	// 根据结果返回状态
	switch {
	case ctx.Err() == context.DeadlineExceeded:
		return AppRunStatus_APP_RUN_ABNORMAL, err
	default:
		return lastKnownStatus, err
	}
}

// waitForAppHealthy 轮询等待应用健康
func (m *DockerAppManager) waitForAppHealthy(ctx context.Context, appName string) error {
	return wait.PollUntilContextTimeout(
		ctx,
		constants.DefaultHealthCheckInterval*time.Second,
		time.Duration(m.GetHealthCheckTimeout())*time.Second,
		true,
		func(ctx context.Context) (bool, error) {
			isReady, err := m.singleHealthCheck(ctx, appName)
			if err != nil {
				logger.Warnf(ctx, "Health check failed for application %s: %v, will retry", appName, err)
				return false, nil // 返回false和nil表示需要重试
			}
			if !isReady {
				logger.Infof(ctx, "Application %s is not ready yet, will retry", appName)
				return false, nil // 返回false和nil表示需要重试
			}
			logger.Infof(ctx, "Application %s is ready", appName)
			return true, nil // 返回true表示检查成功，停止重试
		},
	)
}

func (m *DockerAppManager) Reset(ctx context.Context, config *AppConfig) error {
	if m.HasResetScript() {
		opt := &util.ExecOption{
			FilePath: m.GetResetScriptPath(),
			CmdName:  constants.ShellCmd,
			Args:     []string{m.GetResetScriptPath()},
			Envs:     nil,
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}

		if err := util.ExecFileIfExists(ctx, opt); err != nil {
			logger.Errorf(ctx, "Reset failed at reset script stage: %v", err)
			return err
		}
	}
	return nil
}
