package service

import (
	"context"
	"fmt"
	"os"
	"time"

	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/sshpass"
	"golang.org/x/sync/errgroup"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/util"
)

// K8sAppManager K8s应用管理器
type K8sAppManager struct {
	PackageInfoProvider
	clientset kubernetes.Interface
}

// NewK8sAppManager 创建K8s应用管理器
func NewK8sAppManager(app string, deployMode constants.DeployModeType, baseDir string) (AppManagerInterface, error) {
	clientset, err := util.GetClientsetFromKubeconfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get k8s clientset: %v", err)
	}

	pkg, err := NewAppPackageInfo(app, deployMode, baseDir)
	if err != nil {
		return nil, err
	}

	return &K8sAppManager{
		PackageInfoProvider: pkg,
		clientset:           clientset,
	}, nil
}

// initializeNodes 在指定的K8s节点上执行初始化脚本
func (m *K8sAppManager) initializeNodes(ctx context.Context, nodeIPs []string, envs map[string]string) error {
	// 获取节点初始化脚本路径
	nodeInitScriptPath := m.GetNodeInitScriptPath()

	// 检查初始化脚本是否存在
	if _, err := os.Stat(nodeInitScriptPath); err != nil {
		logger.Warnf(ctx, "Node initialization script not found at %s, skipping initialization", nodeInitScriptPath)
		return nil
	}

	if len(nodeIPs) == 0 {
		logger.Warnf(ctx, "No nodes specified for initialization")
		return nil
	}

	// 创建错误组用于并发控制
	g, ctx := errgroup.WithContext(ctx)

	// 并发执行每个节点的初始化脚本
	for _, nodeIP := range nodeIPs {
		nodeIP := nodeIP // 创建副本以避免闭包问题
		g.Go(func() error {
			return m.initializeNodesByIP(ctx, nodeIP, nodeInitScriptPath, envs, m.GetComponentPath())
		})
	}

	// 等待所有节点完成
	if err := g.Wait(); err != nil {
		logger.Errorf(ctx, "Node initialization failed: %v", err)
		return fmt.Errorf("node initialization failed: %w", err)
	}

	logger.Infof(ctx, "Initialization script execution completed on all nodes")
	return nil
}

// initializeNodesByIP 通过IP在单个节点上执行初始化脚本
func (m *K8sAppManager) initializeNodesByIP(ctx context.Context, nodeIP string, scriptPath string, envs map[string]string, workDir string) error {
	logger.Infof(ctx, "Starting initialization script execution on node %s", nodeIP)
	client, err := sshpass.NewSSHNoPassword("root", nodeIP)
	if err != nil {
		logger.Errorf(ctx, "Failed to create SSH client: %v", err)
		return fmt.Errorf("failed to create SSH client: %w", err)
	}

	cmd := fmt.Sprintf("cd %s && %s", workDir, scriptPath)
	if len(envs) > 0 {
		// 将环境变量转换为字符串格式
		for k, v := range envs {
			client.WithEnv(k, v)
		}
	}
	output, err := client.WithNotTimeout().ExecRemoteCmd(ctx, cmd)

	if err != nil {
		logger.Errorf(ctx, "Failed to execute initialization script on node %s: %v, output: %s", nodeIP, err, output)
		return fmt.Errorf("failed to execute initialization script on node %s: %w", nodeIP, err)
	}

	logger.Infof(ctx, "Initialization script execution completed on node %s, output: %s", nodeIP, output)
	return nil
}

// Install 安装应用
func (m *K8sAppManager) Install(ctx context.Context, config *AppConfig) error {
	logger.Infof(ctx, "Starting installation of application: %s", config.Name)

	envMap, err := util.ConvertYamlToEnvMap(config.EnvFilePath)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert environment variables: %v", err)
		return err
	}

	// 获取namespace
	namespace := m.GetNamespace()

	// 获取k8s节点
	nodeIPs, err := m.getK8sNodes(ctx)
	if err != nil {
		logger.Errorf(ctx, "Failed to get nodes: %v", err)
		return err
	}

	if err = m.initializeNodes(ctx, nodeIPs, envMap); err != nil {
		logger.Errorf(ctx, "Failed to initialize nodes")
	}

	// 执行安装前脚本
	logger.Infof(ctx, "Executing pre-installation script")
	preInstallScriptPath := m.GetPreInstallScriptPath()
	execOpts := &util.ExecOption{
		Args:       []string{preInstallScriptPath},
		CmdName:    constants.ShellCmd,
		Envs:       envMap,
		FilePath:   preInstallScriptPath,
		SetHome:    true,
		WorkDir:    m.GetComponentPath(),
		NotTimeout: true,
	}
	if err := util.ExecFileIfExists(ctx, execOpts); err != nil {
		logger.Errorf(ctx, "Installation failed at pre-installation script stage")
		return err
	}

	// 执行helm安装
	logger.Infof(ctx, "Installing Helm chart: %s in namespace: %s", config.Name, namespace)
	chartPath := m.GetChartPath()

	// 准备helm安装命令参数
	helmArgs := []string{constants.HelmInstallCmd, config.Name, chartPath, constants.HelmNamespaceFlag, namespace, constants.HelmCreateNSFlag}

	// 如果EnvFilePath不为空，才添加values文件参数
	if config.EnvFilePath != "" {
		helmArgs = append(helmArgs, constants.HelmValuesFlag, config.EnvFilePath)
	}

	helmCmdExecOpts := &util.ExecOption{
		FilePath: chartPath,
		CmdName:  constants.HelmCmd,
		Args:     helmArgs,
		Envs:     envMap,
		WorkDir:  m.GetComponentPath(),
		SetHome:  true,
	}

	if err := util.ExecFileIfExists(ctx, helmCmdExecOpts); err != nil {
		logger.Errorf(ctx, "Installation failed at helm chart installation stage")
		return err
	}

	// 安装完后，对应用进行健康检查
	logger.Infof(ctx, "Starting health check for application: %s", config.Name)

	err = m.waitForAppHealthy(ctx, config.Name)
	if err != nil {
		logger.Errorf(ctx, "Health check failed for application %s after timeout: %v", config.Name, err)
		return fmt.Errorf("installation failed at health check stage: %w", err)
	}

	logger.Infof(ctx, "Application %s installation completed successfully in namespace %s", config.Name, namespace)
	return nil
}

// Uninstall 卸载应用
func (m *K8sAppManager) Uninstall(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Uninstalling application: %s", appName)

	switch m.HasUninstallScript() {
	case true:
		// 执行卸载脚本
		logger.Infof(ctx, "Executing uninstallation script if exists")
		uninstallScriptPath := m.GetUninstallScriptPath()
		execOpts := &util.ExecOption{
			FilePath: uninstallScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{uninstallScriptPath},
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}

		if err := util.ExecFileIfExists(ctx, execOpts); err != nil {
			logger.Errorf(ctx, "Uninstallation failed at uninstallation script stage: %v", err)
			return err
		}
		logger.Infof(ctx, "Application %s has been uninstalled successfully", appName)
	case false:
		// 获取namespace
		namespace := m.GetNamespace()

		// 执行helm卸载
		logger.Infof(ctx, "Executing helm uninstall in namespace: %s", namespace)
		chartPath := m.GetChartPath()

		execOpts := &util.ExecOption{
			FilePath: chartPath,
			CmdName:  constants.HelmCmd,
			Args:     []string{constants.HelmUninstallCmd, appName, constants.HelmNamespaceFlag, namespace},
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, execOpts); err != nil {
			logger.Errorf(ctx, "Uninstallation failed at helm uninstall stage: %v", err)
			return err
		}
		logger.Infof(ctx, "Application %s uninstallation completed successfully in namespace %s", appName, namespace)
	}

	return nil
}

// Start 启动应用
func (m *K8sAppManager) Start(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Starting application: %s", appName)

	switch m.HasStartScript() {
	case true:
		// 执行启动脚本
		logger.Infof(ctx, "Executing startup script")
		startScriptPath := m.GetStartScriptPath()
		execOpts := &util.ExecOption{
			FilePath: startScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{startScriptPath},
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, execOpts); err != nil {
			logger.Errorf(ctx, "Failed to start application at startup script phase: %v", err)
			return err
		}
	case false:
		// 获取应用命名空间
		namespace := m.GetNamespace()

		// 使用helm upgrade --reuse-values启动应用
		chartPath := m.GetChartPath()
		execOpts := &util.ExecOption{
			FilePath: chartPath,
			CmdName:  constants.HelmCmd,
			Args:     []string{constants.HelmUpgradeCmd, appName, chartPath, constants.HelmReuseValuesFlag, constants.HelmNamespaceFlag, namespace},
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, execOpts); err != nil {
			logger.Errorf(ctx, "Failed to start application %s: %v", appName, err)
			return err
		}
	}
	// 启动后进行健康检查
	logger.Infof(ctx, "Starting health check for application: %s", appName)
	if err := m.waitForAppHealthy(ctx, appName); err != nil {
		logger.Errorf(ctx, "Health check failed for application %s after timeout: %v", appName, err)
		return fmt.Errorf("start failed at health check stage: %w", err)
	}

	logger.Infof(ctx, "Application %s start and health check completed successfully", appName)
	return nil
}

// Stop 停止应用
func (m *K8sAppManager) Stop(ctx context.Context, appName string) error {
	logger.Infof(ctx, "Stopping application: %s", appName)

	switch m.HasStopScript() {
	case true:
		stopScriptPath := m.GetStopScriptPath()
		execOpts := util.ExecOption{
			SetHome:  true,
			FilePath: stopScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{stopScriptPath},
			WorkDir:  m.GetComponentPath(),
		}

		if err := util.ExecFileIfExists(ctx, &execOpts); err != nil {
			logger.Errorf(ctx, "Failed to stop application at stop script stage: %v", err)
		}
		logger.Infof(ctx, "Application %s stopped successfully", appName)
	case false:
		if err := m.stopK8sResources(ctx, appName); err != nil {
			return err
		}
	}
	return nil
}

// stopK8sResources 停止 K8s 相关资源（Deployments、StatefulSets、DaemonSets）
func (m *K8sAppManager) stopK8sResources(ctx context.Context, appName string) error {
	namespace := m.GetNamespace()

	// 停止 Deployments
	deployments, err := m.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get Deployment list: %v", err)
		return err
	}

	// 停止 StatefulSets
	statefulsets, err := m.clientset.AppsV1().StatefulSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get StatefulSet list: %v", err)
		return err
	}

	// 停止 DaemonSets
	daemonsets, err := m.clientset.AppsV1().DaemonSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get DaemonSet list: %v", err)
		return err
	}

	var stopErrors []error

	// 处理 Deployments
	for _, deployment := range deployments.Items {
		if isPartOfHelmRelease(deployment.Annotations, appName) {
			replicas := int32(0)
			deployment.Spec.Replicas = &replicas
			_, err := m.clientset.AppsV1().Deployments(namespace).Update(ctx, &deployment, metav1.UpdateOptions{})
			if err != nil {
				logger.Errorf(ctx, "Failed to update replicas for Deployment %s: %v", deployment.Name, err)
				stopErrors = append(stopErrors, err)
			} else {
				logger.Infof(ctx, "Successfully set replicas to 0 for Deployment %s", deployment.Name)
			}
		}
	}

	// 处理 StatefulSets
	for _, statefulset := range statefulsets.Items {
		if isPartOfHelmRelease(statefulset.Annotations, appName) {
			replicas := int32(0)
			statefulset.Spec.Replicas = &replicas
			_, err := m.clientset.AppsV1().StatefulSets(namespace).Update(ctx, &statefulset, metav1.UpdateOptions{})
			if err != nil {
				logger.Errorf(ctx, "Failed to update replicas for StatefulSet %s: %v", statefulset.Name, err)
				stopErrors = append(stopErrors, err)
			} else {
				logger.Infof(ctx, "Successfully set replicas to 0 for StatefulSet %s", statefulset.Name)
			}
		}
	}

	// 处理 DaemonSets
	for _, daemonset := range daemonsets.Items {
		if isPartOfHelmRelease(daemonset.Annotations, appName) {
			if daemonset.Spec.Template.Spec.NodeSelector == nil {
				daemonset.Spec.Template.Spec.NodeSelector = make(map[string]string)
			}
			daemonset.Spec.Template.Spec.NodeSelector[constants.NonExistentLabelKey] = constants.NonExistentLabelValue
			_, err := m.clientset.AppsV1().DaemonSets(namespace).Update(ctx, &daemonset, metav1.UpdateOptions{})
			if err != nil {
				logger.Errorf(ctx, "Failed to update node selector for DaemonSet %s: %v", daemonset.Name, err)
				stopErrors = append(stopErrors, err)
			} else {
				logger.Infof(ctx, "Successfully updated node selector for DaemonSet %s", daemonset.Name)
			}
		}
	}

	if len(stopErrors) > 0 {
		logger.Errorf(ctx, "Errors occurred while stopping components of application %s in namespace %s", appName, namespace)
		return fmt.Errorf("failed to stop application components")
	}

	logger.Infof(ctx, "Application %s has been successfully stopped in namespace %s", appName, namespace)
	return nil
}

// Status 检查应用状态
func (m *K8sAppManager) Status(ctx context.Context, appName string) (AppRunStatus, error) {
	ctx, cancel := context.WithTimeout(ctx, constants.DefaultSingleHealthCheckTimeout*time.Second)
	defer cancel()
	return m.healthCheckWithTimeout(ctx, appName)
}

func (m *K8sAppManager) healthCheckWithTimeout(ctx context.Context, appName string) (AppRunStatus, error) {
	var (
		status          bool
		lastKnownStatus AppRunStatus
		err             error
	)

	status, err = m.singleHealthCheck(ctx, appName)
	if err != nil {
		logger.Warnf(ctx, "Health check failed for application %s: %v", appName, err)
	}

	switch status {
	case true:
		lastKnownStatus = AppRunStatus_APP_RUN_NORMAL
	case false:
		lastKnownStatus = AppRunStatus_APP_RUN_ABNORMAL
	}

	// 根据结果返回状态
	switch {
	case ctx.Err() == context.DeadlineExceeded:
		return AppRunStatus_APP_RUN_ABNORMAL, err
	default:
		return lastKnownStatus, err
	}
}

func (m *K8sAppManager) singleHealthCheck(ctx context.Context, appName string) (bool, error) {
	logger.Infof(ctx, "Checking application status: %s", appName)

	switch m.HasHealthCheckScript() {
	case true:
		// 执行健康检查脚本
		healthCheckScriptPath := m.GetHealthCheckScriptPath()
		execOpts := util.ExecOption{
			FilePath: healthCheckScriptPath,
			CmdName:  constants.ShellCmd,
			Args:     []string{healthCheckScriptPath},
			WorkDir:  m.GetComponentPath(),
			SetHome:  true,
		}
		if err := util.ExecFileIfExists(ctx, &execOpts); err != nil {
			logger.Errorf(ctx, "Health check failed for application %s: %v", appName, err)
			return false, err
		}

		logger.Infof(ctx, "Application %s health check script executed successfully", appName)
		return true, nil
	case false:
		return m.checkK8sResourcesHealthy(ctx, appName)
	}

	return true, nil
}

// checkK8sResourcesHealthy 检查 K8s 相关资源（Deployments、StatefulSets、DaemonSets）健康状态
func (m *K8sAppManager) checkK8sResourcesHealthy(ctx context.Context, appName string) (bool, error) {
	namespace := m.GetNamespace()

	// 检查 Deployments
	deployments, err := m.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get Deployment list: %v", err)
		return false, err
	}

	for _, deployment := range deployments.Items {
		if isPartOfHelmRelease(deployment.Annotations, appName) {
			if deployment.Status.ReadyReplicas == 0 {
				logger.Warnf(ctx, "Deployment %s has no ready replicas", deployment.Name)
				return false, nil
			}
		}
	}

	// 检查 StatefulSets
	statefulsets, err := m.clientset.AppsV1().StatefulSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get StatefulSet list: %v", err)
		return false, err
	}

	for _, statefulset := range statefulsets.Items {
		if isPartOfHelmRelease(statefulset.Annotations, appName) {
			if statefulset.Status.ReadyReplicas != statefulset.Status.Replicas {
				logger.Warnf(ctx, "StatefulSet %s is unhealthy: ready replicas %d/%d",
					statefulset.Name, statefulset.Status.ReadyReplicas, statefulset.Status.Replicas)
				return false, nil
			}
		}
	}

	// 检查 DaemonSets
	daemonsets, err := m.clientset.AppsV1().DaemonSets(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "Failed to get DaemonSet list: %v", err)
		return false, err
	}

	for _, daemonset := range daemonsets.Items {
		if isPartOfHelmRelease(daemonset.Annotations, appName) {
			if daemonset.Status.NumberReady != daemonset.Status.DesiredNumberScheduled {
				logger.Warnf(ctx, "DaemonSet %s is unhealthy: ready replicas %d/%d",
					daemonset.Name, daemonset.Status.NumberReady, daemonset.Status.DesiredNumberScheduled)
				return false, nil
			}
		}
	}
	logger.Infof(ctx, "Application %s is running healthy in namespace %s", appName, namespace)
	return true, nil
}

// 辅助函数：检查注解是否表明资源属于指定应用
func isPartOfHelmRelease(annotations map[string]string, releaseName string) bool {
	if value, exists := annotations[constants.HelmReleaseNameAnnotation]; exists && value == releaseName {
		return true
	}
	return false
}

// getK8sNodes 获取K8s集群中的所有节点IP
func (m *K8sAppManager) getK8sNodes(ctx context.Context) ([]string, error) {
	nodes, err := m.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get node list: %w", err)
	}

	var nodeIPs []string
	for _, node := range nodes.Items {
		if len(node.Status.Addresses) > 0 {
			nodeIPs = append(nodeIPs, node.Status.Addresses[0].Address)
		}
	}

	return nodeIPs, nil
}

// Init 初始化节点
func (m *K8sAppManager) Init(ctx context.Context, config *AppConfig) error {
	if len(config.NodeIPs) == 0 {
		return fmt.Errorf("no node IPs provided")
	}

	envMap, err := util.ConvertYamlToEnvMap(config.EnvFilePath)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert environment variables: %v", err)
		return err
	}
	logger.Debugf(ctx, "Nodes: %s, Env: %s", config.NodeIPs, envMap)

	if err = m.initializeNodes(ctx, config.NodeIPs, envMap); err != nil {
		logger.Errorf(ctx, "Failed to initialize nodes: %v", err)
		return err
	}
	return nil
}

// waitForAppHealthy 轮询等待应用健康
func (m *K8sAppManager) waitForAppHealthy(ctx context.Context, appName string) error {
	return wait.PollUntilContextTimeout(
		ctx,
		constants.DefaultHealthCheckInterval*time.Second,
		time.Duration(m.GetHealthCheckTimeout())*time.Second,
		true,
		func(ctx context.Context) (bool, error) {
			isReady, err := m.singleHealthCheck(ctx, appName)
			if err != nil {
				logger.Warnf(ctx, "Health check failed for application %s: %v, will retry", appName, err)
				return false, nil // 返回false和nil表示需要重试
			}
			if !isReady {
				logger.Infof(ctx, "Application %s is not ready yet, will retry", appName)
				return false, nil // 返回false和nil表示需要重试
			}
			logger.Infof(ctx, "Application %s is ready", appName)
			return true, nil // 返回true表示检查成功，停止重试
		},
	)
}

func (m *K8sAppManager) Reset(ctx context.Context, config *AppConfig) error {
	nodeIPs := config.NodeIPs

	if len(nodeIPs) == 0 {
		return fmt.Errorf("no node IPs provided")
	}

	if !m.HasResetScript() {
		logger.Warnf(ctx, "Reset script not found for application %s", config.Name)
		return nil
	}

	// 创建错误组用于并发控制
	g, ctx := errgroup.WithContext(ctx)

	// 并发执行每个节点的重置脚本
	for _, nodeIP := range nodeIPs {
		nodeIP := nodeIP // 创建副本以避免闭包问题
		g.Go(func() error {
			client, err := sshpass.NewSSHNoPassword("root", nodeIP)
			if err != nil {
				logger.Errorf(ctx, "Failed to create SSH client for node %s: %v", nodeIP, err)
				return fmt.Errorf("failed to create SSH client for node %s: %w", nodeIP, err)
			}

			cmd := fmt.Sprintf("cd %s && %s", m.GetComponentPath(), m.GetResetScriptPath())
			output, err := client.WithNotTimeout().ExecRemoteCmd(ctx, cmd)
			if err != nil {
				logger.Errorf(ctx, "Failed to execute reset script on node %s: %v, output: %s", nodeIP, err, output)
				return fmt.Errorf("failed to execute reset script on node %s: %w", nodeIP, err)
			}

			logger.Infof(ctx, "Successfully executed reset script on node %s", nodeIP)
			return nil
		})
	}

	// 等待所有节点完成
	if err := g.Wait(); err != nil {
		logger.Errorf(ctx, "Application reset failed: %v", err)
		return fmt.Errorf("aApplication reset failed: %w", err)
	}

	logger.Infof(ctx, "Successfully reset application: %s", config.Name)
	return nil
}
