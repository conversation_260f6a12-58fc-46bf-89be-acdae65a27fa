package service

import (
	"context"
	"fmt"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	runtimesvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/service"
)

// AppConfig 应用配置
type AppConfig struct {
	// 应用名称
	Name string `json:"name" yaml:"name"`

	// 环境变量文件路径(Docker-Compose)/值文件路径(Chart包)
	EnvFilePath string `json:"envFilePath" yaml:"envFilePath"`

	// 节点IP
	NodeIPs []string `json:"nodeIPs" yaml:"nodeIPs"`
}

// AppRunStatus 应用运行状态枚举
type AppRunStatus bool

const (
	AppRunStatus_APP_RUN_NORMAL   AppRunStatus = true  // 正常
	AppRunStatus_APP_RUN_ABNORMAL AppRunStatus = false // 异常
)

// AppManagerInterface 应用管理接口
type AppManagerInterface interface {
	// Install 安装应用
	Install(ctx context.Context, config *AppConfig) error
	// Uninstall 卸载应用
	Uninstall(ctx context.Context, appName string) error
	// Start 启动应用
	Start(ctx context.Context, appName string) error
	// Stop 停止应用
	Stop(ctx context.Context, appName string) error
	// Status 检查应用是否健康（true=健康，false=不健康）
	Status(ctx context.Context, appName string) (AppRunStatus, error)
	// Init 安装应用前节点的初始化
	Init(ctx context.Context, config *AppConfig) error
	// Reset 重置应用
	Reset(ctx context.Context, config *AppConfig) error
}

// NewAppManager 创建应用管理器
func NewAppManager(runtimeType runtimesvc.RuntimeType, app string, deployMode constants.DeployModeType, baseDir string) (AppManagerInterface, error) {
	switch runtimeType {
	case runtimesvc.RuntimeTypeDocker:
		return NewDockerAppManager(app, baseDir)
	case runtimesvc.RuntimeTypeKube:
		return NewK8sAppManager(app, deployMode, baseDir)
	default:
		return nil, fmt.Errorf("unsupported runtime type: %v", runtimeType)
	}
}

// PackageInfoProvider 应用包信息提供者接口
// todo 目前阶段先写死，待后续应用包规范确定下来再具体实现
type PackageInfoProvider interface {
	// GetChartPath 获取Helm Chart路径
	GetChartPath() string
	// GetDockerComposePath 获取Docker Compose文件路径
	GetDockerComposePath() string
	// GetNodeInitScriptPath 获取组件节点初始化脚本路径
	GetNodeInitScriptPath() string
	// GetPreInstallScriptPath 获取安装前脚本路径
	GetPreInstallScriptPath() string
	// GetHealthCheckScriptPath 获取健康检查脚本路径
	GetHealthCheckScriptPath() string
	// GetUninstallScriptPath 获取卸载脚本路径
	GetUninstallScriptPath() string
	// GetStartScriptPath 获取启动脚本路径
	GetStartScriptPath() string
	// GetStopScriptPath 获取停止脚本路径
	GetStopScriptPath() string
	// GetNamespace 获取应用命名空间
	GetNamespace() string
	// GetComponentPath 获取组件路径
	GetComponentPath() string
	// HasHealthCheckScript 检查健康检查脚本是否存在
	HasHealthCheckScript() bool
	// GetHealthCheckTimeout 获取健康检查超时时间
	GetHealthCheckTimeout() int64
	// HasStartScript 启动脚本是否存在
	HasStartScript() bool
	// HasStopScript 停止脚本是否存在
	HasStopScript() bool
	// HasUninstallScript 卸载脚本是否存在
	HasUninstallScript() bool
	// GetResetScriptPath 获取重置脚本路径
	GetResetScriptPath() string
	// HasResetScript 重置脚本是否存在
	HasResetScript() bool
}
