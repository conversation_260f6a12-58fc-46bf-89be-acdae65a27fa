package constants

// BaseDir 应用包基础目录
const BaseDir = "/opt/tongyong/installer/"

// 目录结构常量
const (
	VendorDirName      = "vendors"
	ComponentsDirName  = "components"
	ScriptsDirName     = "scripts"
	NodeScriptsDirName = "node_scripts"
)

// 文件名常量
const (
	DockerComposeFileName     = "docker-compose.yaml"
	NodeInitScriptFileName    = "node_init.sh"
	PreInstallScriptFileName  = "pre_install.sh"
	HealthCheckScriptFileName = "health_check.sh"
	UninstallScriptFileName   = "uninstall.sh"
	StartScriptFileName       = "start.sh"
	StopScriptFileName        = "stop.sh"
	ResetScriptFileName       = "reset.sh"
)

// 命令常量
const (
	DockerComposeCmd = "docker-compose"
	HelmCmd          = "helm"
	ShellCmd         = "sh"
)

// Helm命令参数常量
const (
	HelmInstallCmd      = "install"
	HelmUninstallCmd    = "uninstall"
	HelmUpgradeCmd      = "upgrade"
	HelmValuesFlag      = "--values"
	HelmReuseValuesFlag = "--reuse-values"
	HelmNamespaceFlag   = "--namespace"
	HelmCreateNSFlag    = "--create-namespace"
)

// Helm注解常量
const (
	HelmReleaseNameAnnotation = "meta.helm.sh/release-name"
)

// Kubernetes标签常量
const (
	// NonExistentLabelKey 用于停止 DaemonSet 的不存在标签键
	NonExistentLabelKey = "non-existent-label"
	// NonExistentLabelValue 用于停止 DaemonSet 的不存在标签值
	NonExistentLabelValue = "true"
)

// Docker-compose命令参数常量
const (
	DockerComposeFileFlag   = "-f"
	DockerComposeEnvFlag    = "--env-file"
	DockerComposeUpCmd      = "up"
	DockerComposeDetachFlag = "-d"
	DockerComposeDownCmd    = "down"
	DockerComposeStartCmd   = "start"
	DockerComposeStopCmd    = "stop"
	DockerComposePsCmd      = "ps"
	DockerComposeAllFlag    = "-a"
)

const (
	DefaultHealthCheckInterval      = 5   // 应用健康检查默认重试间隔时间
	DefaultHealthCheckTimeout       = 600 // 应用健康检查默认超时时间
	DefaultSingleHealthCheckTimeout = 360 // 单次应用健康检查默认超时时间
)
