package util

import (
	"os"
	"strings"
	"testing"
)

func TestConvertYamlToEnvFile(t *testing.T) {
	tests := []struct {
		name        string
		yamlContent string
		wantErr     bool
		checkFunc   func(t *testing.T, envFile string)
	}{
		{
			name: "正常配置",
			yamlContent: `
database:
  host: localhost
  port: 5432
  username: admin
app:
  name: myapp
  version: 1.0.0
`,
			wantErr: false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				envVars := string(content)
				expectedVars := []string{
					"DATABASE_HOST=localhost",
					"DATABASE_PORT=5432",
					"DATABASE_USERNAME=admin",
					"APP_NAME=myapp",
					"APP_VERSION=1.0.0",
				}
				for _, expected := range expectedVars {
					if !strings.Contains(envVars, expected) {
						t.<PERSON><PERSON><PERSON>("环境变量文件中未找到预期的变量: %s", expected)
					}
				}
			},
		},
		{
			name: "包含数组的配置",
			yamlContent: `
app:
  ports:
    - 8080
    - 8081
`,
			wantErr: true,
		},
		{
			name:        "空配置",
			yamlContent: "",
			wantErr:     false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				if len(string(content)) != 0 {
					t.Error("空配置应该生成空的环境变量文件")
				}
			},
		},
		{
			name: "特殊字符配置",
			yamlContent: `
app:
  special: "hello:world"
  spaces: "hello world"
`,
			wantErr: false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				envVars := string(content)
				expectedVars := []string{
					"APP_SPECIAL=hello:world",
					"APP_SPACES=hello world",
				}
				for _, expected := range expectedVars {
					if !strings.Contains(envVars, expected) {
						t.Errorf("环境变量文件中未找到预期的变量: %s", expected)
					}
				}
			},
		},
		{
			name: "嵌套配置",
			yamlContent: `
app:
  database:
    master:
      host: localhost
      port: 3306
    slave:
      host: 127.0.0.1
      port: 3307
`,
			wantErr: false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				envVars := string(content)
				expectedVars := []string{
					"APP_DATABASE_MASTER_HOST=localhost",
					"APP_DATABASE_MASTER_PORT=3306",
					"APP_DATABASE_SLAVE_HOST=127.0.0.1",
					"APP_DATABASE_SLAVE_PORT=3307",
				}
				for _, expected := range expectedVars {
					if !strings.Contains(envVars, expected) {
						t.Errorf("环境变量文件中未找到预期的变量: %s", expected)
					}
				}
			},
		},
		{
			name: "布尔值配置",
			yamlContent: `
features:
  enabled: true
  debug: false
`,
			wantErr: false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				envVars := string(content)
				expectedVars := []string{
					"FEATURES_ENABLED=true",
					"FEATURES_DEBUG=false",
				}
				for _, expected := range expectedVars {
					if !strings.Contains(envVars, expected) {
						t.Errorf("环境变量文件中未找到预期的变量: %s", expected)
					}
				}
			},
		},
		{
			name: "数值配置",
			yamlContent: `
limits:
  cpu: 2
  memory: 1024
  ratio: 0.75
`,
			wantErr: false,
			checkFunc: func(t *testing.T, envFile string) {
				content, err := os.ReadFile(envFile)
				if err != nil {
					t.Errorf("无法读取生成的环境变量文件: %v", err)
					return
				}
				envVars := string(content)
				expectedVars := []string{
					"LIMITS_CPU=2",
					"LIMITS_MEMORY=1024",
					"LIMITS_RATIO=0.75",
				}
				for _, expected := range expectedVars {
					if !strings.Contains(envVars, expected) {
						t.Errorf("环境变量文件中未找到预期的变量: %s", expected)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时YAML文件
			tmpYaml, err := os.CreateTemp("", "test-*.yaml")
			if err != nil {
				t.Fatalf("无法创建临时YAML文件: %v", err)
			}
			defer os.Remove(tmpYaml.Name())

			if _, err := tmpYaml.WriteString(tt.yamlContent); err != nil {
				t.Fatalf("无法写入YAML内容: %v", err)
			}
			tmpYaml.Close()

			// 执行转换
			envFile, err := ConvertYamlToEnvFile(tmpYaml.Name())
			if (err != nil) != tt.wantErr {
				t.Errorf("ConvertYamlToEnvFile() 错误 = %v, 期望错误 = %v", err, tt.wantErr)
				return
			}

			// 如果成功生成了环境变量文件，确保最后删除它
			if envFile != "" {
				defer os.Remove(envFile)
			}

			// 如果有检查函数并且没有错误，执行检查
			if tt.checkFunc != nil && !tt.wantErr {
				tt.checkFunc(t, envFile)
			}
		})
	}
}

func TestConvertYamlToEnvFile_FileErrors(t *testing.T) {
	// 测试不存在的文件
	_, err := ConvertYamlToEnvFile("nonexistent.yaml")
	if err == nil {
		t.Error("对不存在的文件应该返回错误")
	}

	// 测试无效的YAML内容
	tmpYaml, err := os.CreateTemp("", "invalid-*.yaml")
	if err != nil {
		t.Fatalf("无法创建临时文件: %v", err)
	}
	defer os.Remove(tmpYaml.Name())

	if _, err := tmpYaml.WriteString("invalid: yaml: content: {"); err != nil {
		t.Fatalf("无法写入无效的YAML内容: %v", err)
	}
	tmpYaml.Close()

	_, err = ConvertYamlToEnvFile(tmpYaml.Name())
	if err == nil {
		t.Error("对无效的YAML内容应该返回错误")
	}
}

func TestConvertYamlToEnvMap(t *testing.T) {
	tests := []struct {
		name        string
		yamlContent string
		wantErr     bool
		wantMap     map[string]string
	}{
		{
			name: "正常配置",
			yamlContent: `
database:
  host: localhost
  port: 5432
  username: admin
app:
  name: myapp
  version: 1.0.0
`,
			wantErr: false,
			wantMap: map[string]string{
				"DATABASE_HOST":     "localhost",
				"DATABASE_PORT":     "5432",
				"DATABASE_USERNAME": "admin",
				"APP_NAME":          "myapp",
				"APP_VERSION":       "1.0.0",
			},
		},
		{
			name: "包含数组的配置",
			yamlContent: `
app:
  ports:
    - 8080
    - 8081
`,
			wantErr: true,
		},
		{
			name:        "空配置",
			yamlContent: "",
			wantErr:     false,
			wantMap:     map[string]string{},
		},
		{
			name: "特殊字符配置",
			yamlContent: `
app:
  special: "hello:world"
  spaces: "hello world"
`,
			wantErr: false,
			wantMap: map[string]string{
				"APP_SPECIAL": "hello:world",
				"APP_SPACES":  "hello world",
			},
		},
		{
			name: "嵌套配置",
			yamlContent: `
app:
  database:
    master:
      host: localhost
      port: 3306
    slave:
      host: 127.0.0.1
      port: 3307
`,
			wantErr: false,
			wantMap: map[string]string{
				"APP_DATABASE_MASTER_HOST": "localhost",
				"APP_DATABASE_MASTER_PORT": "3306",
				"APP_DATABASE_SLAVE_HOST":  "127.0.0.1",
				"APP_DATABASE_SLAVE_PORT":  "3307",
			},
		},
		{
			name: "布尔值和数值配置",
			yamlContent: `
features:
  enabled: true
  debug: false
limits:
  cpu: 2
  memory: 1024
  ratio: 0.75
`,
			wantErr: false,
			wantMap: map[string]string{
				"FEATURES_ENABLED": "true",
				"FEATURES_DEBUG":   "false",
				"LIMITS_CPU":       "2",
				"LIMITS_MEMORY":    "1024",
				"LIMITS_RATIO":     "0.75",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时YAML文件
			tmpYaml, err := os.CreateTemp("", "test-*.yaml")
			if err != nil {
				t.Fatalf("无法创建临时YAML文件: %v", err)
			}
			defer os.Remove(tmpYaml.Name())

			if _, err := tmpYaml.WriteString(tt.yamlContent); err != nil {
				t.Fatalf("无法写入YAML内容: %v", err)
			}
			tmpYaml.Close()

			// 执行转换
			gotMap, err := ConvertYamlToEnvMap(tmpYaml.Name())
			if (err != nil) != tt.wantErr {
				t.Errorf("ConvertYamlToEnvMap() 错误 = %v, 期望错误 = %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// 检查map大小是否相同
				if len(gotMap) != len(tt.wantMap) {
					t.Errorf("ConvertYamlToEnvMap() 返回的map大小 = %v, 期望大小 = %v", len(gotMap), len(tt.wantMap))
					return
				}

				// 检查每个键值对是否匹配
				for k, v := range tt.wantMap {
					if gotV, exists := gotMap[k]; !exists {
						t.Errorf("ConvertYamlToEnvMap() 缺少键 %v", k)
					} else if gotV != v {
						t.Errorf("ConvertYamlToEnvMap()[%v] = %v, 期望值 = %v", k, gotV, v)
					}
				}
			}
		})
	}
}

func TestConvertYamlToEnvMap_FileErrors(t *testing.T) {
	// 测试不存在的文件
	_, err := ConvertYamlToEnvMap("nonexistent.yaml")
	if err == nil {
		t.Error("对不存在的文件应该返回错误")
	}

	// 测试无效的YAML内容
	tmpYaml, err := os.CreateTemp("", "invalid-*.yaml")
	if err != nil {
		t.Fatalf("无法创建临时文件: %v", err)
	}
	defer os.Remove(tmpYaml.Name())

	if _, err := tmpYaml.WriteString("invalid: yaml: content: {"); err != nil {
		t.Fatalf("无法写入无效的YAML内容: %v", err)
	}
	tmpYaml.Close()

	_, err = ConvertYamlToEnvMap(tmpYaml.Name())
	if err == nil {
		t.Error("对无效的YAML内容应该返回错误")
	}
}
