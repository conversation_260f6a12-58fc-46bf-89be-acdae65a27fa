package util

import (
	"context"
	"fmt"
	"os"
	"strings"

	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/process"
)

type ExecOption struct {
	SetHome    bool
	FilePath   string
	CmdName    string
	Args       []string
	Envs       map[string]string
	WorkDir    string
	NotTimeout bool
}

func (e *ExecOption) GetSetHome() bool {
	if e == nil {
		return false
	}
	return e.SetHome
}

// ExecFileIfExists 检查文件是否存在，如果存在则执行命令，否则记录日志并跳过
// 这是一个通用工具函数，供DockerAppManager和K8sAppManager等使用
func ExecFileIfExists(ctx context.Context, opts *ExecOption) error {
	cmdName := opts.CmdName
	filePath := opts.FilePath
	args := opts.Args
	envs := opts.Envs

	if _, err := os.Stat(opts.FilePath); err != nil {
		if os.IsNotExist(err) {
			// 文件不存在，记录信息并跳过
			logger.Infof(ctx, "File does not exist, skipping execution: %s", filePath)
			return nil
		}
		return fmt.Errorf("failed to check file %s: %w", filePath, err)
	}

	// 文件存在，执行命令
	logger.Debugf(ctx, "File exists, executing command %s with file %s", cmdName, filePath)
	if opts.GetSetHome() {
		oldCmd := append([]string{cmdName}, args...)
		newCmd := "sh"
		newArgs := []string{"-c", fmt.Sprintf("export HOME=/root && %s", strings.Join(oldCmd, " "))}

		cmdName = newCmd
		args = newArgs
	}
	logger.Debugf(ctx, "cmdName: %s, args: %v, envs: %v", cmdName, args, envs)
	executor := process.NewExecutor(cmdName).IsRoot().WithWorkDir(opts.WorkDir)
	if opts.NotTimeout {
		executor = executor.WithNotTimeout()
	}
	if len(args) > 0 {
		executor = executor.WithArgs(args...)
	}
	if len(envs) > 0 {
		// 将环境变量转换为字符串格式
		envVars := make([]string, 0, len(envs))
		for k, v := range envs {
			envVars = append(envVars, fmt.Sprintf("%s=%s", k, v))
		}
		executor = executor.WithEnv(envVars...)
	}
	// 执行命令
	logger.Debugf(ctx, "Command: %s %s", cmdName, strings.Join(args, " "))
	exitCode, output, err := executor.ExecRetAll(ctx)
	if err != nil || exitCode != 0 {
		logger.Errorf(ctx, "Command %s failed with exit code %d: %v, output: %s", cmdName, exitCode, err, output)
		return fmt.Errorf("command %s failed with exit code %d: %v, output: %s", cmdName, exitCode, err, output)
	}
	logger.Debugf(ctx, "Command %s executed successfully with output: %s", cmdName, output)
	return nil
}
