package util

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// ConvertYamlToEnvFile 将YAML格式的配置文件转换为环境变量格式并写入临时文件
func ConvertYamlToEnvFile(yamlPath string) (string, error) {
	// 读取YAML文件内容
	data, err := os.ReadFile(yamlPath)
	if err != nil {
		return "", fmt.Errorf("failed to read YAML file: %v", err)
	}

	// 将YAML内容解析为map结构
	var yamlConfig map[string]interface{}
	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return "", fmt.Errorf("failed to parse YAML file: %v", err)
	}

	// 创建临时文件用于存储环境变量
	tmpFile, err := os.CreateTemp("", "env-*")
	if err != nil {
		return "", fmt.Errorf("failed to create temporary file: %v", err)
	}
	defer tmpFile.Close()

	// 将YAML配置转换为环境变量并写入文件
	// 使用匿名函数处理每个键值对，将其写入文件
	if err := processYamlConfig(yamlConfig, func(envKey, envValue string) error {
		_, err := fmt.Fprintf(tmpFile, "%s=%s\n", envKey, envValue)
		return err
	}); err != nil {
		os.Remove(tmpFile.Name()) // 如果处理失败，清理临时文件
		return "", fmt.Errorf("failed to write environment variables: %v", err)
	}

	return tmpFile.Name(), nil
}

// ConvertYamlToEnvMap 将YAML格式的配置文件转换为环境变量映射
func ConvertYamlToEnvMap(yamlPath string) (map[string]string, error) {
	// 读取YAML文件内容
	data, err := os.ReadFile(yamlPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read YAML file: %v", err)
	}

	// 将YAML内容解析为map结构
	var yamlConfig map[string]interface{}
	if err := yaml.Unmarshal(data, &yamlConfig); err != nil {
		return nil, fmt.Errorf("failed to parse YAML file: %v", err)
	}

	// 创建环境变量映射
	envMap := make(map[string]string)

	// 将YAML配置转换为环境变量映射
	// 使用匿名函数处理每个键值对，将其存储到map中
	if err := processYamlConfig(yamlConfig, func(envKey, envValue string) error {
		envMap[envKey] = envValue
		return nil
	}); err != nil {
		return nil, fmt.Errorf("failed to convert to environment variables: %v", err)
	}

	return envMap, nil
}

// processYamlConfig 递归处理YAML结构并处理每个键值对
//
// 示例:
// 转换前：
// key1: value1
// key2:
//
//	key3: value3
//
// 转换后：
// KEY1=value1
// KEY2_KEY3=value3
func processYamlConfig(data interface{}, processor func(envKey, envValue string) error) error {
	// 定义内部递归函数，用于处理嵌套的YAML结构
	var processValue func(currentPrefix string, value interface{}) error
	processValue = func(currentPrefix string, value interface{}) error {
		switch v := value.(type) {
		case map[string]interface{}:
			// 处理嵌套的map结构
			for key, val := range v {
				// 构建新的前缀，如果是嵌套的key，则用下划线连接
				newPrefix := key
				if currentPrefix != "" {
					newPrefix = currentPrefix + "_" + key
				}
				// 递归处理嵌套的值
				if err := processValue(strings.ToUpper(newPrefix), val); err != nil {
					return err
				}
			}
		case []interface{}:
			// 暂不支持数组类型的配置
			return fmt.Errorf("array type configuration is not supported")
		default:
			// 处理叶子节点，调用处理器函数处理键值对
			return processor(currentPrefix, fmt.Sprintf("%v", v))
		}
		return nil
	}

	// 从根节点开始处理
	return processValue("", data)
}
