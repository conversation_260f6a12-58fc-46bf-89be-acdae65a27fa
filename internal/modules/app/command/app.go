package command

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	appsvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/service"
	metasvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/meta/service"
	"gitlab.bingosoft.net/cloud-public/logger"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	runtimesvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/service"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon/base"
)

var (
	appFlags = AppFlags{}
	// 用于临时存储命令行参数
	deployModeFlag string
	// 基础目录
	baseDir string
)

// AppFlags 自定义参数
type AppFlags struct {
	Name       string                   // 应用名称
	EnvFile    string                   // 环境配置文件
	DeployMode constants.DeployModeType // 部署模式
	NodeIPs    string                   // 节点IP列表，以逗号分隔
}

// AppStatusResponse 应用状态响应结构体
type AppStatusResponse struct {
	Healthy appsvc.AppRunStatus `json:"healthy"`
	Error   string              `json:"error"`
}

func NewAppCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "app",
		Short: "操作应用部署相关命令",
	}
	cmd.AddCommand(newAppInstallCommand())
	cmd.AddCommand(newAppUninstallCommand())
	cmd.AddCommand(newAppStartCommand())
	cmd.AddCommand(newAppStopCommand())
	cmd.AddCommand(newAppStatusCommand())
	cmd.AddCommand(newAppInfoCommand())
	cmd.AddCommand(newAppInitCommand())
	cmd.AddCommand(newAppResetCommand())

	cmd.Example = fmt.Sprintf("%s %s action [options]", daemon.GetExeName(), cmd.Use)
	// 只处理部署模式参数
	cmd.PersistentPreRunE = func(cmd *cobra.Command, args []string) error {
		// 设置部署模式，如果未指定则使用标准模式
		appFlags.DeployMode = constants.DeployModeType(deployModeFlag)
		if appFlags.DeployMode == "" {
			appFlags.DeployMode = constants.DeployModeStandard
		}
		return nil
	}

	// 添加全局部署模式参数
	cmd.PersistentFlags().StringVar(&deployModeFlag, "deploy-mode", "", "--deploy-mode=standard|optimized")
	cmd.PersistentFlags().StringVar(&baseDir, "base-dir", "/opt/tongyong/installer", "--base-dir=/path")
	return cmd
}

// GetRuntimeTypeFromMeta 从元数据获取运行时类型
func GetRuntimeTypeFromMeta() (runtimesvc.RuntimeType, error) {
	// 获取元数据
	meta, err := metasvc.GetMeta()
	if err != nil {
		return "", err
	}

	// 直接使用Meta中的RuntimeType属性
	if meta.RuntimeType == "" {
		return "", fmt.Errorf("runtime type in metadata is empty")
	}

	// 检查是否为支持的运行时类型
	switch meta.RuntimeType {
	case runtimesvc.RuntimeTypeKube, runtimesvc.RuntimeTypeDocker:
		return meta.RuntimeType, nil
	default:
		return "", fmt.Errorf("unsupported runtime type: %s", meta.RuntimeType)
	}
}

func newAppInstallCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "install <app_name>",
		Short: "安装应用",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		// 从配置文件加载应用配置
		logger.Info(ctx, fmt.Sprintf("Installing application: %s, environment file: %s", appName, appFlags.EnvFile))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}

		// 执行安装
		return appManager.Install(ctx, &appsvc.AppConfig{
			Name:        appName,
			EnvFilePath: appFlags.EnvFile,
		})
	})
	cmd.PersistentFlags().StringVar(&appFlags.EnvFile, "env", "", "--env=config.yaml")
	return cmd
}

func newAppUninstallCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "uninstall <app_name>",
		Short: "卸载应用",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Uninstalling application: %s", appName))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}
		// 执行卸载
		return appManager.Uninstall(ctx, appName)
	})
	return cmd
}

func newAppStartCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "start <app_name>",
		Short: "启动应用",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Starting application: %s", appName))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}
		// 执行启动
		return appManager.Start(ctx, appName)
	})
	return cmd
}

func newAppStopCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "stop <app_name>",
		Short: "停止应用",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Stopping application: %s", appName))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}
		// 执行停止
		return appManager.Stop(ctx, appName)
	})
	return cmd
}

func newAppStatusCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "status",
		Short: "检查应用状态",
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Checking application status: %s", appName))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}

		// 执行状态检查并获取健康状态
		appStatus, err := appManager.Status(ctx, appName)
		// 输出健康状态
		response := AppStatusResponse{
			Healthy: appStatus,
		}
		if err != nil {
			response.Error = err.Error()
		}
		base.PrintJSON(response)
		return nil
	})
	return cmd
}

func newAppInfoCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "info",
		Short: "查看所有应用信息",
	}, func(ctx context.Context, args []string) error {
		// 获取所有组件信息
		components, err := appsvc.GetAllComponentsInfo(ctx, appFlags.DeployMode, baseDir)
		if err != nil {
			return fmt.Errorf("failed to get app information: %v", err)
		}

		// 输出组件信息
		if len(components) == 0 {
			logger.Info(ctx, "No app information found")
			return nil
		}

		base.PrintJSON(components)
		return nil
	})

	return cmd
}

func newAppInitCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "init <app_name>",
		Short: "初始化应用节点",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Initializing application nodes: %s", appName))

		// 从元数据获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}

		// 根据运行时类型处理节点IP
		var nodeIPs []string
		if runtimeType == runtimesvc.RuntimeTypeKube {
			// 解析节点IP列表
			nodeIPs = strings.Split(appFlags.NodeIPs, ",")
			logger.Debugf(ctx, "Node IPs: %v", nodeIPs)
			if len(nodeIPs) == 0 {
				return fmt.Errorf("no node IPs provided for kubernetes runtime")
			}
		}

		logger.Debugf(ctx, "Initializing application nodes: %s, nodeIPs: %s", appName, nodeIPs)
		// 执行初始化
		return appManager.Init(ctx, &appsvc.AppConfig{
			Name:        appName,
			EnvFilePath: appFlags.EnvFile,
			NodeIPs:     nodeIPs,
		})
	})

	// 添加命令行参数
	cmd.Flags().StringVar(&appFlags.NodeIPs, "ips", "", "--ips=ip1,ip2,ip3 (仅用于集群模式下)")
	cmd.Flags().StringVar(&appFlags.EnvFile, "env", "", "--env=config.yaml")
	return cmd
}

func newAppResetCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "reset <app_name>",
		Short: "重置应用",
		Args:  cobra.ExactArgs(1),
	}, func(ctx context.Context, args []string) error {
		appName := args[0]
		logger.Info(ctx, fmt.Sprintf("Resetting application: %s", appName))

		// 获取运行时类型
		runtimeType, err := GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %v", err)
		}

		// 创建应用管理器
		appManager, err := appsvc.NewAppManager(runtimeType, appName, appFlags.DeployMode, baseDir)
		if err != nil {
			return err
		}

		// 根据运行时类型处理节点IP
		var nodeIPs []string
		if runtimeType == runtimesvc.RuntimeTypeKube {
			// 解析节点IP列表
			if appFlags.NodeIPs == "" {
				return fmt.Errorf("no node IPs provided for kubernetes runtime")
			}
			nodeIPs = strings.Split(appFlags.NodeIPs, ",")
			logger.Debugf(ctx, "Node IPs: %v", nodeIPs)
		}

		// 调用 Reset
		return appManager.Reset(ctx, &appsvc.AppConfig{
			Name:        appName,
			EnvFilePath: appFlags.EnvFile,
			NodeIPs:     nodeIPs,
		})
	})
	cmd.Flags().StringVar(&appFlags.NodeIPs, "ips", "", "--ips=ip1,ip2")
	return cmd
}
