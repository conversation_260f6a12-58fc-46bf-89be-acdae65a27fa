package service

import (
	"encoding/json"
	"os"
	"path/filepath"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/meta/constants"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/service"
)

// Meta 定义元数据结构体
type Meta struct {
	// 运行时类型属性
	RuntimeType service.RuntimeType `json:"runtimeType"`
}

// SaveMeta 将元数据结构体保存为JSON文件
func SaveMeta(meta *Meta) error {
	// 确保目录存在
	dir := filepath.Dir(constants.MetadataFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 将结构体转换为JSON
	data, err := json.MarshalIndent(meta, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(constants.MetadataFilePath, data, 0644)
}

// GetMeta 从JSON文件读取元数据并反序列化为结构体
func GetMeta() (*Meta, error) {
	// 读取文件内容
	data, err := os.ReadFile(constants.MetadataFilePath)
	if err != nil {
		return nil, err
	}

	// 将JSON转换为结构体
	var meta Meta
	if err := json.Unmarshal(data, &meta); err != nil {
		return nil, err
	}

	return &meta, nil
}
