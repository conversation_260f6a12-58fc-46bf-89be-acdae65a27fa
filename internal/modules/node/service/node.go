package service

import (
	"context"
	"fmt"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	runtimesvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/service"
)

// NodeManagerInterface 节点管理接口
type NodeManagerInterface interface {
	// Init 安装应用前节点的初始化
	Init(ctx context.Context) error
}

// NewNodeManager 创建节点管理器
func NewNodeManager(runtimeType runtimesvc.RuntimeType, options ...NodeManagerOption) (NodeManagerInterface, error) {
	opts := &nodeManagerOptions{}
	for _, opt := range options {
		opt(opts)
	}

	switch runtimeType {
	case runtimesvc.RuntimeTypeDocker:
		return NewDockerNodeManager(opts.baseDir)
	case runtimesvc.RuntimeTypeKube:
		return NewKubeNodeManager(opts.ips, opts.deployMode, opts.baseDir)
	default:
		return nil, fmt.Errorf("unsupported runtime type: %v", runtimeType)
	}
}

// NodeManagerOption 是配置 NodeManager 的函数类型
type NodeManagerOption func(*nodeManagerOptions)

// nodeManagerOptions 包含所有可能的选项
type nodeManagerOptions struct {
	ips        []string
	deployMode constants.DeployModeType
	baseDir    string
}

// WithIPs 设置 IP 列表的选项
func WithIPs(ips []string) NodeManagerOption {
	return func(o *nodeManagerOptions) {
		o.ips = ips
	}
}

// WithDeployMode 设置部署模式的选项
func WithDeployMode(mode constants.DeployModeType) NodeManagerOption {
	return func(o *nodeManagerOptions) {
		o.deployMode = mode
	}
}

// WithBaseDir 设置基础目录选项
func WithBaseDir(baseDir string) NodeManagerOption {
	return func(o *nodeManagerOptions) {
		o.baseDir = baseDir
	}
}
