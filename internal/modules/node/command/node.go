package command

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/command"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	nodesvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/node/service"
	runtimesvc "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/service"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon/base"
	"gitlab.bingosoft.net/cloud-public/logger"
)

// NodeFlags 自定义参数
type NodeFlags struct {
	NodeIPs    string                   // 节点IP列表，以逗号分隔
	DeployMode constants.DeployModeType // 部署模式：standard 或 optimized
}

var (
	nodeFlags = NodeFlags{}
	// 用于临时存储命令行参数
	deployModeFlag string
)

func NewNodeCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "node",
		Short: "节点相关命令",
	}
	cmd.AddCommand(
		newNodeInitCommand(),
	)
	// 只处理部署模式参数
	cmd.PersistentPreRunE = func(cmd *cobra.Command, args []string) error {
		// 设置部署模式，如果未指定则使用标准模式
		nodeFlags.DeployMode = constants.DeployModeType(deployModeFlag)
		if nodeFlags.DeployMode == "" {
			nodeFlags.DeployMode = constants.DeployModeStandard
		}
		return nil
	}
	cmd.Example = fmt.Sprintf("%s %s action [options]", daemon.GetExeName(), cmd.Use)
	return cmd
}

// newNodeInitCommand 创建节点初始化命令
func newNodeInitCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "init",
		Short: "初始化节点",
	}, func(ctx context.Context, args []string) error {
		// 获取运行时类型
		runtimeType, err := command.GetRuntimeTypeFromMeta()
		if err != nil {
			return fmt.Errorf("failed to get runtime type: %w", err)
		}

		// 构建节点管理器选项
		options, err := buildNodeManagerOptions(ctx, runtimeType)
		if err != nil {
			return err
		}

		// 创建节点管理器
		nodeManager, err := nodesvc.NewNodeManager(runtimeType, options...)
		if err != nil {
			return fmt.Errorf("failed to create node manager: %w", err)
		}

		// 执行节点初始化
		if err := nodeManager.Init(ctx); err != nil {
			return fmt.Errorf("failed to initialize node: %w", err)
		}

		logger.Infof(ctx, "Node initialization completed successfully")
		return nil
	})

	// 添加命令行参数
	cmd.Flags().StringVar(&nodeFlags.NodeIPs, "ips", "", "节点IP列表，以逗号分隔，如--ips ip1,ip2")
	cmd.Flags().StringVar(&deployModeFlag, "deploy-mode", "standard", "部署模式：standard 或 optimized")
	return cmd
}

// buildNodeManagerOptions 根据运行时类型构建节点管理器选项
func buildNodeManagerOptions(ctx context.Context, runtimeType runtimesvc.RuntimeType) ([]nodesvc.NodeManagerOption, error) {
	var options []nodesvc.NodeManagerOption

	// 只在 Kubernetes 运行时模式下处理节点IP和部署模式
	if runtimeType == runtimesvc.RuntimeTypeKube {
		// 解析并验证节点IP列表
		nodeIPs, err := parseAndValidateNodeIPs(nodeFlags.NodeIPs)
		if err != nil {
			return nil, err
		}

		options = append(options,
			nodesvc.WithIPs(nodeIPs),
			nodesvc.WithDeployMode(nodeFlags.DeployMode),
		)

		logger.Debugf(ctx, "Runtime configuration - Type: %s, NodeIPs: %v, DeployMode: %s",
			runtimeType, nodeIPs, nodeFlags.DeployMode)
	}

	return options, nil
}

// parseAndValidateNodeIPs 解析并验证节点IP列表
func parseAndValidateNodeIPs(ips string) ([]string, error) {
	if ips == "" {
		return nil, fmt.Errorf("node IP list cannot be empty in Kubernetes runtime mode")
	}

	nodeIPs := strings.Split(ips, ",")
	for i, ip := range nodeIPs {
		nodeIPs[i] = strings.TrimSpace(ip)
		if nodeIPs[i] == "" {
			return nil, fmt.Errorf("node IP list contains empty values")
		}
	}

	return nodeIPs, nil
}
