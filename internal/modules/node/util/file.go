package util

import (
	"fmt"
	"os"
	"path/filepath"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
)

// FindNodeScripts 查找所有vendor下node_scripts目录中指定部署模式的脚本
func FindNodeScripts(deployMode constants.DeployModeType, baseDir string) ([]string, error) {
	var scripts []string

	vendorsDir := filepath.Join(baseDir, "vendors")
	if _, err := os.Stat(vendorsDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("vendors directory not found: %s", vendorsDir)
	}

	// 遍历 vendors 下的所有厂商目录
	vendorEntries, err := os.ReadDir(vendorsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read vendors directory: %w", err)
	}

	for _, vendorEntry := range vendorEntries {
		if !vendorEntry.IsDir() {
			continue
		}

		vendorName := vendorEntry.Name()
		scriptsDir := filepath.Join(vendorsDir, vendorName, constants.NodeScriptsDirName, string(deployMode))

		// 直接使用 Glob 匹配 .sh 文件，避免 Walk 整个目录
		shFiles, err := filepath.Glob(filepath.Join(scriptsDir, "*.sh"))
		if err != nil {
			continue
		}

		if len(shFiles) == 0 {
			continue
		}

		scripts = append(scripts, shFiles...)
	}

	return scripts, nil
}
