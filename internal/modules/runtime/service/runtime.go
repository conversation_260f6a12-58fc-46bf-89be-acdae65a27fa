package service

import (
	"context"
)

// RuntimeType 表示运行时类型
type RuntimeType string

const (
	// RuntimeTypeDocker Docker运行时
	RuntimeTypeDocker RuntimeType = "docker"
	// RuntimeTypeKube K8s运行时
	RuntimeTypeKube RuntimeType = "kube"
)

// RuntimeConfig 运行时配置
type RuntimeConfig struct {
	// Docker配置
	Docker *DockerConfig `json:"docker,omitempty" yaml:"docker,omitempty"`
	// K8s配置
	K8s *K8sConfig `json:"k8s,omitempty" yaml:"k8s,omitempty"`
}

// DockerConfig Docker运行时配置
type DockerConfig struct {
}

// K8sConfig K8s运行时配置
type K8sConfig struct {
}

// RuntimeInterface 定义统一的运行时接口
type RuntimeInterface interface {
	// Install 安装运行时环境
	Install(ctx context.Context, config *RuntimeConfig) error
}
