package command

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon/base"
)

var (
	flags = RuntimeFlags{}
)

// RuntimeFlags 自定义参数
type RuntimeFlags struct {
	Type string // 运行时类型
}

func NewRuntimeCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "runtime",
		Short: "安装容器运行时相关命令",
	}
	cmd.AddCommand(newRuntimeInstallCommand())
	cmd.Example = fmt.Sprintf("%s %s action [options]", daemon.GetExeName(), cmd.Use)
	return cmd
}

func newRuntimeInstallCommand() *cobra.Command {
	cmd := base.NewCommand(base.CommandMeta{
		Use:   "install",
		Short: "安装运行时",
	}, func(ctx context.Context, args []string) error {
		// todo 添加安装逻辑
		return nil
	})
	cmd.PersistentFlags().StringVar(&flags.Type, "type", "", "--type=docker|kube")
	return cmd
}
