package daemon

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon/base"
)

var (
	_rootCmd *cobra.Command
	_exeName = filepath.Base(os.Args[0])
)

func Init(fnVer func() error, fnCmd func(*cobra.Command)) {
	cobra.EnableCommandSorting = false

	_rootCmd = &cobra.Command{
		Use:          _exeName + " [command] (flags)",
		Short:        _exeName + " command-line interface and server",
		SilenceUsage: true,
	}
	_rootCmd.SetFlagErrorFunc(func(c *cobra.Command, err error) error {
		if e := c.Usage(); e != nil {
			return e
		}
		fmt.Fprintln(c.OutOrStderr())
		return err
	})
	_rootCmd.PersistentFlags().StringVar(&base.GlobalFlags.ConfigPath, "config", "", "cover default config file")
	_rootCmd.PersistentFlags().StringVarP(&base.GlobalFlags.OutputFormat, "write-out", "w", "", "set the output format (json, simple, table,field)")
	_rootCmd.PersistentFlags().StringVar(&base.GlobalFlags.LogDir, "log-dir", "", "cover default config log dir")
	_rootCmd.PersistentFlags().BoolVarP(&base.GlobalFlags.DetailLogFlag, "log", "l", false, "print detail log")
	versionCmd := &cobra.Command{
		Use:   "version",
		Short: "Show version information",
		RunE: func(*cobra.Command, []string) error {
			if fnVer != nil {
				return fnVer()
			}
			return nil
		},
	}

	_rootCmd.AddCommand(
		versionCmd,
	)

	if fnCmd != nil {
		fnCmd(_rootCmd)
	}
}

func Run() {
	os.Setenv("LANG", "en_US.UTF-8")

	args := os.Args
	if len(args) == 1 {
		args = append(args, "--help")
	}
	_rootCmd.SetArgs(args[1:])
	if err := _rootCmd.Execute(); err != nil {
		os.Exit(2)
	}
}

func GetExeName() string {
	return _exeName
}
