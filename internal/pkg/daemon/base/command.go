package base

import (
	"context"
	"os"

	"github.com/spf13/cobra"
	"gitlab.bingosoft.net/cloud-public/logger"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/appcfg"
)

var (
	GlobalFlags = GlobalFlag{}
)

type GlobalFlag struct {
	OutputFormat  string
	ConfigPath    string
	LogDir        string
	DetailLogFlag bool
}

type ToolResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    ` json:"data"`
}

type CommandMeta struct {
	Use, Short, Example string
	Args                cobra.PositionalArgs
}

func NewCommand(meta CommandMeta, fn func(ctx context.Context, args []string) error) *cobra.Command {
	tmpCmd := &cobra.Command{
		Use:     meta.Use,
		Short:   meta.Short,
		Example: meta.Example,
		Args:    meta.Args,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := context.Background()
			appcfg.Init(ctx, GlobalFlags.ConfigPath, GlobalFlags.LogDir, GlobalFlags.DetailLogFlag)
			defer func() {
				logger.Sync()
			}()
			err := fn(ctx, args)
			if err != nil {
				PrintError(err)
				os.Exit(2)
			}
			//PrintSuccess("Success")
			return nil
		},
	}
	return tmpCmd
}

func NewCommandPrint(meta CommandMeta, fn func(ctx context.Context) ([]InfoBase, error)) *cobra.Command {
	tmpCmd := &cobra.Command{
		Use:     meta.Use,
		Short:   meta.Short,
		Example: meta.Example,
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx := context.Background()
			appcfg.Init(ctx, GlobalFlags.ConfigPath, GlobalFlags.LogDir, GlobalFlags.DetailLogFlag)
			defer func() {
				logger.Sync()
			}()
			ret, err := fn(ctx)
			if err != nil {
				return err
			}
			logger.Debug(ctx, "执行命令成功", logger.Any("ret", ret))
			switch GlobalFlags.OutputFormat {
			case "table":
				PrintTable(ret)
			case "json":
				PrintJSON(ret)
			case "field":
				PrintFields(ret)
			case "simple":
				PrintSimple(ret)
			default:
				PrintSuccess(ret)
			}
			return nil
		},
	}

	return tmpCmd
}

func MarkFlagsRequired(cmd *cobra.Command, names ...string) {
	for _, name := range names {
		if err := cmd.MarkFlagRequired(name); err != nil {
			panic(err)
		}
	}
}
