package base

import (
	"os"

	"github.com/olekukonko/tablewriter"
)

func PrintTable(tableInfos []InfoBase) {
	if len(tableInfos) == 0 {
		return
	}
	hdr, rows := makeListTable(tableInfos)
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader(hdr)
	for _, row := range rows {
		table.Append(row)
	}
	table.SetAlignment(tablewriter.ALIGN_RIGHT)
	table.Render()
}

func makeListTable(tableInfos []InfoBase) (hdr []string, rows [][]string) {
	if len(tableInfos) == 0 {
		return []string{}, nil
	}
	hdr = tableInfos[0].Hdr()
	for _, info := range tableInfos {
		if info == nil {
			continue
		}
		rows = append(rows, info.Row())
	}
	return hdr, rows
}
