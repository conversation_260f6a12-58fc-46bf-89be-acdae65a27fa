package appcfg

import (
	"context"

	"gitlab.bingosoft.net/cloud-public/cfg"
	"gitlab.bingosoft.net/cloud-public/daemon"
	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/fileutil"
	"gopkg.in/yaml.v3"
)

var AppConf Config

type Config struct {
	Global Global `mapstructure:"global"  yaml:"global" json:"global"`
}

func Init(ctx context.Context, configPath, logDir string, detailLog bool) {
	var err error
	if len(configPath) == 0 || !fileutil.FileExists(configPath) {
		err = yaml.Unmarshal([]byte(defaultConfig), &AppConf)
	} else {
		err = cfg.LoadConfig(&AppConf, daemon.ConfigFilePath())
	}
	if err != nil {
		logger.Critical(ctx, "Failed to load configuration file", logger.Err(err))
		panic(err)
	}
	if len(logDir) != 0 {
		AppConf.Global.Logger.Directory = logDir
	}
	if detailLog {
		AppConf.Global.Logger.LogInConsole = true
	}
	logger.Init(AppConf.Global.Logger)
	//logger.Info(ctx, "Configuration file loaded successfully", logger.Any("config", AppConf))
}
