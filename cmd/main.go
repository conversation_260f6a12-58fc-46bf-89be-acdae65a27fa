package main

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/spf13/cobra"
	nodecommand "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/node/command"
	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/timeutil"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/build"
	appcommand "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/command"
	runtimecommand "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/runtime/command"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/pkg/daemon"
)

func main() {
	daemon.Init(printVersion, initCommand)
	daemon.Run()
}

func printVersion() error {
	ctx := context.Background()
	var bTim string
	tim, err := time.ParseInLocation(timeutil.StdDateTime, build.Build, time.UTC)
	if err == nil {
		bTim = tim.Local().Format(timeutil.StdDateTime)
	} else {
		bTim = build.Build
	}

	logger.Info(ctx, fmt.Sprintf("AppName:\t%s\n", build.AppName))
	logger.Info(ctx, fmt.Sprintf("Version:\t%s\n", build.Version))
	logger.Info(ctx, fmt.Sprintf("Commit:\t%s\n", build.GitVersion))
	logger.Info(ctx, fmt.Sprintf("Build:\t%s\n", bTim))
	logger.Info(ctx, fmt.Sprintf("Compiler:\t%s\n", runtime.Version()))
	logger.Info(ctx, fmt.Sprintf("Branch:\t%s\n", build.Branch))
	logger.Info(ctx, fmt.Sprintf("Message:\t%s\n", build.Message))
	return nil
}
func initCommand(cmd *cobra.Command) {
	cmd.AddCommand(appcommand.NewAppCommand())
	cmd.AddCommand(runtimecommand.NewRuntimeCommand())
	cmd.AddCommand(nodecommand.NewNodeCommand())
}
