BINARY      := dep-tool

BUILDDIR    := build
VERSION     := 0.0.1
RV			:= $(shell date "+%Y%m%d%H%M%S")
VERSIONFILE := $(BUILDDIR)/version.go
SCRIPTSDIR  := scripts
SERVICEFILE := $(BINARY).service

DBGFLAGS    := -tags quincy,debug
RELFLAGS    := -tags quincy,release -ldflags "-w -s"
ENV         :=
BINGOREPO   := gitlab.bingosoft.net
CNGOPROXY   := https://goproxy.cn,direct
ARCH		:=$(shell uname -m)

build: prepare gensrc
	[ -n "${ENV}" ] && export ${ENV}; \
	export GOPROXY=${CNGOPROXY}; \
	export GOINSECURE=${BINGOREPO}; \
	export GOPRIVATE=${BINGOREPO}; \
	GO111MODULE=on go build -o ${BINARY} ${DBGFLAGS} ./cmd

tidy: prepare gensrc
	export GOPROXY=${CNGOPROXY}; \
	export GOINSECURE=${BINGOREPO}; \
	export GOPRIVATE=${BINGOREPO}; \
	go mod tidy

release-var:
	$(eval DBGFLAGS := ${RELFLAGS})

release: release-var build

prepare:
	mkdir -p "$(BUILDDIR)"

gensrc:
	@[ -f /usr/bin/dnf-3 ] && git config --global --replace-all safe.directory '*'; \
	GV=`git rev-parse HEAD`; \
	BT=`TZ=UTC date "+%F %T"`; \
	MSG=`git log -1 --pretty=%B`; \
	BRANCH=`git rev-parse --abbrev-ref HEAD`; \
	if [ "$$BRANCH" = "HEAD" ] || [ -z "$$BRANCH" ]; then \
		BRANCH=`git describe --long 2>/dev/null || echo "unknown"`; \
	fi; \
	echo "package build" >${VERSIONFILE}; \
	echo >>${VERSIONFILE}; \
	echo "const (" >>${VERSIONFILE}; \
	echo "	AppName    = \"${BINARY}\"" >>${VERSIONFILE}; \
	echo "	Version    = \"${VERSION}\"" >>${VERSIONFILE}; \
	echo "	GitVersion = \"$$GV\"" >>${VERSIONFILE}; \
	echo "	Build      = \"$$BT\"" >>${VERSIONFILE}; \
	echo "	Branch     = \"$$BRANCH\"" >>${VERSIONFILE}; \
	echo "	Message    = \`$$MSG\`" >>${VERSIONFILE}; \
	echo ")" >>${VERSIONFILE}

linux-env:
	@if [ "$(ARCH)" = "aarch64" ]; then \
      echo "GOOS=linux GOARCH=arm64"; \
      export ENV="GOOS=linux GOARCH=arm64"; \
    else \
      echo "GOOS=linux GOARCH=amd64"; \
	  export ENV="GOOS=linux GOARCH=amd64"; \
    fi

linux-env-amd64:
	$(eval ENV := GOOS=linux GOARCH=amd64)
linux-env-arm64:
	$(eval ENV := GOOS=linux GOARCH=arm64)
linux-arm64: linux-env-arm64 build

linux-amd64: linux-env-amd64 build

linux:	linux-arm64

clean:
	rm -f "${BINARY}"

rpm: linux-env release
	D=`mktemp -d`; \
	mkdir -p "$$D/SOURCES"; \
	tar -zcf "$$D/SOURCES/${BINARY}-${VERSION}.tar.gz" \
	Makefile \
	${BINARY}; \
	rpmbuild ${SCRIPTSDIR}/rpm.spec --bb \
	--define "_topdir $$D" \
	--target=${ARCH} \
	--define "releaseVer ${RV}" \
	--define "version ${VERSION}"; \
	cp -ar "$$D/RPMS" .; \
	rm -rf "$$D"

install:
	mkdir -p ${DESTDIR}/usr/bin

	install -m 755 ${BINARY} ${DESTDIR}/usr/bin/${BINARY}

install-release: release
	mkdir -p ${DESTDIR}/usr/bin

	install -m 755 ${BINARY} ${DESTDIR}/usr/bin/${BINARY}
