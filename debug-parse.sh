#!/bin/bash

# 简单测试解析函数

# 解析镜像列表文件
parse_image_file() {
    local file="$1"
    local images=()

    if [[ ! -f "$file" ]]; then
        echo "镜像列表文件不存在: $file" >&2
        exit 1
    fi

    echo "解析镜像列表文件: $file" >&2

    while IFS= read -r line; do
        # 去除前后空格
        line=$(echo "$line" | xargs)

        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^#.* ]]; then
            continue
        fi

        images+=("$line")
        echo "添加镜像: $line" >&2
    done < "$file"

    if [[ ${#images[@]} -eq 0 ]]; then
        echo "镜像列表文件中没有找到有效的镜像" >&2
        exit 1
    fi

    echo "找到 ${#images[@]} 个镜像" >&2
    printf '%s\n' "${images[@]}"
}

echo "=== 测试解析函数 ===" >&2
images_output=$(parse_image_file "image.txt")
echo "=== 函数输出 ===" >&2
echo "$images_output"

echo "=== 使用mapfile测试 ===" >&2
mapfile -t test_images < <(parse_image_file "image.txt")
echo "数组长度: ${#test_images[@]}" >&2
for i in "${!test_images[@]}"; do
    echo "[$i]: ${test_images[$i]}" >&2
done
