### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# This is where the result of the go build goes
/output*/
/_output*/
/_output/
_output

# OSX trash
.DS_Store

# IDE config
.idea/
*.iml
.vscode/
.vscode

log/
# kubeverse dev config
config-dev.yaml
# User cluster configs
.kubeconfig

# 前端编译包
template/

# Vim-related files
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist

artifact/*
test/*

logs/*
docs/*
debug/*
dep-tool
.cursorrules


build
