#!/bin/bash

# 测试镜像可访问性脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试单个镜像
test_image() {
    local image="$1"
    log_info "测试镜像: $image"
    
    # 测试拉取
    if docker pull "$image" >/dev/null 2>&1; then
        log_success "✓ 拉取成功: $image"
        
        # 检查镜像是否存在
        if docker images "$image" --format "{{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
            log_success "✓ 镜像已存在本地: $image"
            
            # 获取镜像大小
            local size
            size=$(docker images "$image" --format "{{.Size}}")
            log_info "  镜像大小: $size"
            return 0
        else
            log_error "✗ 镜像拉取后未找到: $image"
            return 1
        fi
    else
        log_error "✗ 拉取失败: $image"
        return 1
    fi
}

# 主函数
main() {
    local image_file="${1:-image.txt}"
    
    if [[ ! -f "$image_file" ]]; then
        log_error "镜像列表文件不存在: $image_file"
        exit 1
    fi
    
    log_info "开始测试镜像可访问性"
    log_info "镜像列表文件: $image_file"
    echo
    
    local total=0
    local success=0
    local failed_images=()
    
    while IFS= read -r line; do
        # 去除前后空格
        line=$(echo "$line" | xargs)
        
        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^#.* ]]; then
            continue
        fi
        
        ((total++))
        echo
        if test_image "$line"; then
            ((success++))
        else
            failed_images+=("$line")
        fi
    done < "$image_file"
    
    # 输出统计结果
    echo
    log_info "========== 测试完成 =========="
    log_info "总镜像数: $total"
    log_success "成功数量: $success"
    
    if [[ ${#failed_images[@]} -gt 0 ]]; then
        log_error "失败数量: ${#failed_images[@]}"
        log_error "失败列表:"
        for failed in "${failed_images[@]}"; do
            log_error "  - $failed"
        done
        exit 1
    else
        log_success "所有镜像测试通过！"
    fi
}

# 执行主函数
main "$@"
