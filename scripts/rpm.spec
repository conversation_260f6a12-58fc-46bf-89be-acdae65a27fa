%define _missing_doc_files_terminate_build 0
%global debug_package %{nil}

%global servicefile dep-tool.service

Summary:       dep-tool controller
Name:          dep-tool
Version:       %version
Release:       0%{?dist}
License:       Commercial
Group:         Applications/System
Source:        %{name}-%{version}.tar.gz
BuildRoot:     %{_tmppath}/%{name}-%{version}-root
Url:           http://www.bingocloud.cn
Vendor:        BingoSoft Co., Ltd.
Packager:      <EMAIL>
BuildRequires: make
BuildRequires: git

%description
dep-tool controller.

%prep
%setup -c

%build

%install
make install DESTDIR=%{buildroot}

%clean
rm -rf "%{buildroot}"

%files
%{_bindir}/dep-tool