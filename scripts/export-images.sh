#!/bin/bash

# 镜像导出脚本
# 用法: ./export-images.sh [镜像列表文件] [输出目录]

set -e

# 默认参数
DEFAULT_IMAGE_FILE="image.txt"
DEFAULT_OUTPUT_DIR="./images"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
镜像导出脚本

用法:
    $0 [选项] [镜像列表文件] [输出目录]

参数:
    镜像列表文件    包含镜像名称的文本文件 (默认: image.txt)
    输出目录        tar包输出目录 (默认: ./images)

选项:
    -h, --help     显示此帮助信息
    -c, --clean    导出前清理输出目录
    -p, --pull     导出前先拉取镜像
    -v, --verbose  详细输出模式

示例:
    $0                                    # 使用默认参数
    $0 image.txt ./output                 # 指定文件和输出目录
    $0 -c -p image.txt ./output          # 清理目录并拉取镜像后导出

镜像列表文件格式:
    每行一个镜像名称，支持注释行(以#开头)
    registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730
    registry.bingosoft.net/bingokube/etcd:3.5.1-0
    # 这是注释行
    registry.bingosoft.net/bingokube/resource-manager:1.0.1-20250730
EOF
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在PATH中"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行或无权限访问"
        exit 1
    fi
}

# 解析镜像列表文件
parse_image_file() {
    local file="$1"
    local images=()
    
    if [[ ! -f "$file" ]]; then
        log_error "镜像列表文件不存在: $file"
        exit 1
    fi

    log_info "解析镜像列表文件: $file"
    
    while IFS= read -r line; do
        # 去除前后空格
        line=$(echo "$line" | xargs)
        
        # 跳过空行和注释行
        if [[ -z "$line" || "$line" =~ ^#.* ]]; then
            continue
        fi
        
        images+=("$line")
    done < "$file"
    
    if [[ ${#images[@]} -eq 0 ]]; then
        log_error "镜像列表文件中没有找到有效的镜像"
        exit 1
    fi
    
    log_info "找到 ${#images[@]} 个镜像"
    printf '%s\n' "${images[@]}"
}

# 生成安全的文件名
generate_safe_filename() {
    local image="$1"
    # 将特殊字符替换为下划线，并移除多余的下划线
    echo "$image" | sed 's/[^a-zA-Z0-9.-]/_/g' | sed 's/__*/_/g' | sed 's/^_\|_$//g'
}

# 拉取镜像
pull_image() {
    local image="$1"
    log_info "拉取镜像: $image"
    
    if docker pull "$image"; then
        log_success "镜像拉取成功: $image"
        return 0
    else
        log_error "镜像拉取失败: $image"
        return 1
    fi
}

# 导出镜像为tar包
export_image() {
    local image="$1"
    local output_dir="$2"
    local safe_name
    local tar_file
    
    safe_name=$(generate_safe_filename "$image")
    tar_file="$output_dir/${safe_name}.tar"
    
    log_info "导出镜像: $image -> $tar_file"
    
    if docker save -o "$tar_file" "$image"; then
        local file_size
        file_size=$(du -h "$tar_file" | cut -f1)
        log_success "镜像导出成功: $image (大小: $file_size)"
        return 0
    else
        log_error "镜像导出失败: $image"
        return 1
    fi
}

# 主函数
main() {
    local image_file="$DEFAULT_IMAGE_FILE"
    local output_dir="$DEFAULT_OUTPUT_DIR"
    local clean_output=false
    local pull_images=false
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_output=true
                shift
                ;;
            -p|--pull)
                pull_images=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                set -x
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "${image_file_set:-}" ]]; then
                    image_file="$1"
                    image_file_set=true
                elif [[ -z "${output_dir_set:-}" ]]; then
                    output_dir="$1"
                    output_dir_set=true
                else
                    log_error "过多的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    log_info "开始镜像导出任务"
    log_info "镜像列表文件: $image_file"
    log_info "输出目录: $output_dir"
    
    # 检查Docker环境
    check_docker
    
    # 创建输出目录
    if [[ "$clean_output" == true && -d "$output_dir" ]]; then
        log_warn "清理输出目录: $output_dir"
        rm -rf "$output_dir"
    fi
    
    mkdir -p "$output_dir"
    
    # 解析镜像列表
    local images
    mapfile -t images < <(parse_image_file "$image_file")
    
    # 统计变量
    local total=${#images[@]}
    local success_count=0
    local failed_images=()
    
    log_info "开始处理 $total 个镜像"
    
    # 处理每个镜像
    for i in "${!images[@]}"; do
        local image="${images[$i]}"
        local current=$((i + 1))
        
        echo
        log_info "处理镜像 [$current/$total]: $image"
        
        # 拉取镜像（如果需要）
        if [[ "$pull_images" == true ]]; then
            if ! pull_image "$image"; then
                failed_images+=("$image (拉取失败)")
                continue
            fi
        fi
        
        # 导出镜像
        if export_image "$image" "$output_dir"; then
            ((success_count++))
        else
            failed_images+=("$image (导出失败)")
        fi
    done
    
    # 输出统计结果
    echo
    log_info "========== 导出完成 =========="
    log_info "总镜像数: $total"
    log_success "成功导出: $success_count"
    
    if [[ ${#failed_images[@]} -gt 0 ]]; then
        log_error "失败数量: ${#failed_images[@]}"
        log_error "失败列表:"
        for failed in "${failed_images[@]}"; do
            log_error "  - $failed"
        done
        exit 1
    else
        log_success "所有镜像导出成功！"
        log_info "输出目录: $output_dir"
        log_info "文件列表:"
        ls -lh "$output_dir"/*.tar 2>/dev/null || log_warn "输出目录中没有tar文件"
    fi
}

# 执行主函数
main "$@"
