# 镜像导出导入脚本

这个工具集提供了便捷的Docker镜像批量导出和导入功能。

## 文件说明

- `export-images.sh` - 镜像导出脚本，将镜像列表中的镜像导出为tar包
- `import-images.sh` - 镜像导入脚本，批量导入tar包中的镜像
- `image.txt` - 镜像列表文件示例

## 快速开始

### 1. 准备镜像列表文件

创建一个文本文件（如 `image.txt`），每行一个镜像名称：

```
registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730
registry.bingosoft.net/bingokube/etcd:3.5.1-0
registry.bingosoft.net/bingokube/resource-manager-web:1.0.0-20250730
registry.bingosoft.net/bingokube/resource-manager:1.0.1-20250730
```

### 2. 导出镜像

```bash
# 给脚本添加执行权限
chmod +x scripts/export-images.sh scripts/import-images.sh

# 基本用法 - 使用默认参数
./scripts/export-images.sh

# 指定镜像列表文件和输出目录
./scripts/export-images.sh image.txt ./output

# 清理输出目录并拉取最新镜像后导出
./scripts/export-images.sh -c -p image.txt ./output

# 查看帮助信息
./scripts/export-images.sh --help
```

### 3. 导入镜像

```bash
# 基本用法 - 导入默认目录中的所有tar包
./scripts/import-images.sh

# 指定tar包目录
./scripts/import-images.sh ./output

# 强制导入（即使镜像已存在）
./scripts/import-images.sh -f ./output

# 查看帮助信息
./scripts/import-images.sh --help
```

## 详细功能

### export-images.sh 功能特性

- ✅ **智能解析**: 自动解析镜像列表文件，支持注释行和空行
- ✅ **安全文件名**: 自动生成安全的tar包文件名
- ✅ **并发控制**: 逐个处理镜像，避免系统资源过载
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **进度显示**: 实时显示处理进度和统计信息
- ✅ **可选拉取**: 支持导出前先拉取最新镜像
- ✅ **目录清理**: 支持导出前清理输出目录

### import-images.sh 功能特性

- ✅ **批量导入**: 自动查找并导入目录中所有tar包
- ✅ **进度跟踪**: 显示导入进度和统计信息
- ✅ **结果展示**: 导入完成后显示镜像列表
- ✅ **错误处理**: 完善的错误处理和失败统计

## 使用场景

### 场景1: 离线环境部署
```bash
# 在有网络的环境中导出镜像
./scripts/export-images.sh -p image.txt ./offline-images

# 将tar包复制到离线环境后导入
./scripts/import-images.sh ./offline-images
```

### 场景2: 镜像备份
```bash
# 备份当前环境的镜像
./scripts/export-images.sh image.txt ./backup-$(date +%Y%m%d)
```

### 场景3: 环境迁移
```bash
# 源环境导出
./scripts/export-images.sh -c -p production-images.txt ./migration

# 目标环境导入
./scripts/import-images.sh ./migration
```

## 镜像列表文件格式

```
# 这是注释行，会被忽略
registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730

# 支持空行

registry.bingosoft.net/bingokube/etcd:3.5.1-0
registry.bingosoft.net/bingokube/resource-manager-web:1.0.0-20250730
registry.bingosoft.net/bingokube/resource-manager:1.0.1-20250730

# 支持不同的镜像仓库
docker.io/nginx:latest
quay.io/prometheus/prometheus:latest
```

## 输出示例

### 导出过程
```
[INFO] 开始镜像导出任务
[INFO] 镜像列表文件: image.txt
[INFO] 输出目录: ./images
[INFO] 解析镜像列表文件: image.txt
[INFO] 找到 4 个镜像
[INFO] 开始处理 4 个镜像

[INFO] 处理镜像 [1/4]: registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730
[INFO] 导出镜像: registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730 -> ./images/registry.bingosoft.net_bingokube_resource-exporter_1.0.2-20250730.tar
[SUCCESS] 镜像导出成功: registry.bingosoft.net/bingokube/resource-exporter:1.0.2-20250730 (大小: 245M)

========== 导出完成 ==========
[INFO] 总镜像数: 4
[SUCCESS] 成功导出: 4
[SUCCESS] 所有镜像导出成功！
```

### 导入过程
```
[INFO] 开始镜像导入任务
[INFO] tar包目录: ./images
[INFO] 查找tar包文件: ./images
[INFO] 找到 4 个tar文件
[INFO] 开始导入 4 个tar包

[INFO] 导入tar包 [1/4]: registry.bingosoft.net_bingokube_resource-exporter_1.0.2-20250730.tar
[SUCCESS] 镜像导入成功: registry.bingosoft.net_bingokube_resource-exporter_1.0.2-20250730.tar (大小: 245M)

========== 导入完成 ==========
[INFO] 总文件数: 4
[SUCCESS] 成功导入: 4
[SUCCESS] 所有镜像导入成功！
```

## 注意事项

1. **权限要求**: 脚本需要Docker操作权限
2. **磁盘空间**: 确保有足够的磁盘空间存储tar包
3. **网络连接**: 使用 `-p` 选项时需要网络连接
4. **文件命名**: tar包文件名会自动转换特殊字符为下划线

## 故障排除

### 常见问题

1. **Docker未运行**
   ```
   [ERROR] Docker 服务未运行或无权限访问
   ```
   解决: 启动Docker服务或检查用户权限

2. **镜像拉取失败**
   ```
   [ERROR] 镜像拉取失败: registry.example.com/image:tag
   ```
   解决: 检查网络连接和镜像仓库访问权限

3. **磁盘空间不足**
   ```
   [ERROR] 镜像导出失败: image:tag
   ```
   解决: 清理磁盘空间或更换输出目录

## 扩展功能

脚本支持通过环境变量进行配置：

```bash
# 设置默认输出目录
export DEFAULT_OUTPUT_DIR="/data/images"

# 设置默认镜像列表文件
export DEFAULT_IMAGE_FILE="/etc/images.txt"
```
